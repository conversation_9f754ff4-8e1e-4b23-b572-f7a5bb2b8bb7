<!-- 在这个文件对每个tab对应的列表进行渲染 -->
<template>
  <view class="content">
    <!-- 这里设置了z-paging加载时禁止自动调用reload方法，自行控制何时reload（懒加载）-->
    <z-paging 
      ref="paging" 
      v-model="dataList" 
      @query="queryList" 
      use-page-scroll 
      :scrollable="false" 
      :hide-empty-view="hideEmptyView"
      :refresher-enabled="false" 
      @contentHeightChanged="contentHeightChanged" 
      :auto="false" 
      :auto-clean-list-when-reload="false"
    >
      <!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
      <view class="item" v-for="(item, index) in dataList" :key="index">
        <view class="item-title">{{ item.title }}</view>
        <view class="item-detail">{{ item.detail }}</view>
        <view class="item-line"></view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
interface DataItem {
  id: string
  title: string
  detail: string
}

interface Props {
  // 当前组件的index，也就是当前组件是swiper中的第几个
  tabIndex: number
  // 当前swiper切换到第几个index
  currentIndex: number
}

const props = withDefaults(defineProps<Props>(), {
  tabIndex: 0,
  currentIndex: 0,
})

const emit = defineEmits<{
  heightChanged: [height: number]
}>()

// v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
const dataList = ref<DataItem[]>([])
const hideEmptyView = ref(true)
const completeFunc = ref<(() => void) | null>(null)

// z-paging实例引用
const paging = ref()

// 监听当前index变化
watch(
  () => props.currentIndex,
  (newVal) => {
    if (newVal === props.tabIndex) {
      // 懒加载，当滑动到当前的item时，才去加载
      nextTick(() => {
        setTimeout(() => {
          paging.value?.reload()
        }, 100)
      })
    }
  },
  { immediate: true }
)

// 查询列表数据
function queryList(pageNo: number, pageSize: number) {
  // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
  // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
  // 模拟请求服务器获取分页数据
  const params = {
    pageNo,
    pageSize,
    type: props.tabIndex + 1,
  }

  // 模拟网络请求
  setTimeout(() => {
    try {
      const mockData: DataItem[] = []
      const startIndex = (pageNo - 1) * pageSize
      
      // 根据不同tab生成不同的模拟数据
      const tabNames = ['推荐', '一寸照', '二寸照', '证件照', '免冠照']
      const tabName = tabNames[props.tabIndex] || '推荐'
      
      for (let i = 0; i < pageSize; i++) {
        const index = startIndex + i + 1
        mockData.push({
          id: `${props.tabIndex}_${index}`,
          title: `${tabName}项目 ${index}`,
          detail: `这是${tabName}分类下的第${index}个项目详情`,
        })
      }
      
      paging.value?.complete(mockData)
      hideEmptyView.value = false
      
      // 请求结束，调用父组件的下拉刷新结束回调函数，使得父组件中的z-paging下拉刷新结束
      if (completeFunc.value) {
        completeFunc.value()
      }
    } catch (error) {
      // 如果请求失败写paging.value?.complete(false)
      paging.value?.complete(false)
      // 请求结束，调用父组件的下拉刷新结束回调函数，使得父组件中的z-paging下拉刷新结束
      if (completeFunc.value) {
        completeFunc.value()
      }
    }
  }, 500)
}

// 页面通知当前子组件刷新列表
function reload(completeCallback?: () => void) {
  // 先把父组件下拉刷新的回调函数存起来
  completeFunc.value = completeCallback || null
  // 调用z-paging的reload方法
  paging.value?.reload()
}

// 当列表高度改变时，通知页面的swiper同步更改高度
function contentHeightChanged(height: number) {
  const finalHeight = dataList.value.length ? height : 0
  // 限制内容最小高度为屏幕可见高度减z-tabs高度。注意，页面中有使用slot="top"插入的view，则此处的minHeight还应该减去slot="top"插入的view的高度
  const minHeight = uni.getSystemInfoSync().windowHeight - uni.upx2px(80)
  emit('heightChanged', Math.max(finalHeight, minHeight))
}

// 页面通知当前子组件加载更多数据
function doLoadMore() {
  paging.value?.doLoadMore()
}

// 页面通知当前子组件清除数据
function clear() {
  paging.value?.clear()
  hideEmptyView.value = true
}

// 暴露方法给父组件调用
defineExpose({
  reload,
  doLoadMore,
  clear,
})
</script>

<style lang="scss" scoped>
/* 注意，1、父节点需要固定高度，z-paging的height:100%才会生效 */
/* 注意，2、请确保z-paging与同级的其他view的总高度不得超过屏幕宽度，以避免超出屏幕高度时页面的滚动与z-paging内部的滚动冲突 */
.content {
  height: 100%;
}

.item {
  position: relative;
  height: 150rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0rpx 30rpx;
}

.item-detail {
  padding: 5rpx 15rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: white;
  background-color: #007aff;
}

.item-line {
  position: absolute;
  bottom: 0rpx;
  left: 0rpx;
  height: 1px;
  width: 100%;
  background-color: #eeeeee;
}
</style>
