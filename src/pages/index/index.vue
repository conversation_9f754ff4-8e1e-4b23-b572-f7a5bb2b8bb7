<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'custom' 表示使用自定义导航栏，可以自定义标题大小
    "navigationStyle": "default",
    "navigationBarTitleText": "首页",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script lang="ts" setup>
defineOptions({
  name: 'Home',
})

// 当前选中的tab
const currentTab = ref(0)

// tab列表数据
const tabList = ref([
  { name: '推荐', value: 'recommend' },
  { name: '一寸照', value: 'one-inch' },
  { name: '二寸照', value: 'two-inch' },
  { name: '证件照', value: 'id-photo' },
  { name: '免冠照', value: 'bareheaded' },
])

// 各个tab对应的列表数据
const listData = ref({
  'recommend': [],
  'one-inch': [],
  'two-inch': [],
  'id-photo': [],
  'bareheaded': [],
})

// z-paging实例引用
const pagingRef = ref()

// 点击搜索框
function handleSearchClick() {
  console.log('点击了搜索框')
  // 这里可以添加搜索相关逻辑
}

// 证件照类型导航
function indexGoNextPage(type: number) {
  console.log('选择证件照类型:', type)
  // 这里可以添加导航逻辑，根据type参数跳转到不同页面
  // type 0: 一寸照片
  // type 1: 二寸照片
}

// 导航到换背景页面
function navigateToCutout() {
  uni.navigateTo({
    url: '/pages/cutout/index',
  })
}

// tab切换事件
function handleTabChange(index: number) {
  currentTab.value = index
  // 切换tab时重新加载数据
  pagingRef.value?.reload()
}

// z-paging查询数据
function queryList(pageNo: number, pageSize: number) {
  const currentTabValue = tabList.value[currentTab.value]?.value || 'recommend'

  // 模拟异步请求数据
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = []
      const startIndex = (pageNo - 1) * pageSize

      // 根据不同tab生成不同的模拟数据
      for (let i = 0; i < pageSize; i++) {
        const index = startIndex + i + 1
        mockData.push({
          id: `${currentTabValue}_${index}`,
          title: `${currentTabValue === 'recommend' ? '推荐' : currentTabValue === 'one-inch' ? '一寸照' : currentTabValue === 'two-inch' ? '二寸照' : currentTabValue === 'id-photo' ? '证件照' : '免冠照'}项目 ${index}`,
          description: `这是${currentTabValue}分类下的第${index}个项目`,
          image: `/static/icon/${currentTabValue === 'one-inch' ? 'one-inch' : currentTabValue === 'two-inch' ? 'two-inch' : 'change-bg'}.png`,
        })
      }

      resolve({
        list: mockData,
        total: 50, // 模拟总数据量
      })
    }, 500)
  })
}

// 测试 uni API 自动引入
onLoad(() => {
  console.log('证件照首页加载完成')
})
</script>

<template>
  <view class="home-container">
    <!-- z-paging容器 -->
    <z-paging
      ref="pagingRef"
      v-model="listData[tabList[currentTab]?.value || 'recommend']"
      :fixed="false"
      :auto="true"
      @query="queryList"
    >
      <!-- 吸顶区域 -->
      <template #top>
        <wd-sticky :offset-top="0">
          <view class="sticky-header bg-white">
            <!-- 搜索框 -->
            <wd-search
              placeholder="搜索证件照名称、尺寸"
              disabled
              hide-cancel
              placeholder-left
              custom-style="margin: 10rpx 20rpx;"
              @click="handleSearchClick"
            />

            <!-- 图片布局 -->
            <view class="m-[15rpx_0_30rpx_0] h-[370rpx] w-full flex">
              <view class="ml-[35rpx]" @click="indexGoNextPage(0)">
                <image src="/static/icon/one-inch.png" class="h-[370rpx] w-[328rpx]" />
              </view>
              <view class="ml-[26rpx] w-[50%] flex flex-col">
                <view class="h-[172rpx] w-[332rpx]" @click="indexGoNextPage(1)">
                  <image src="/static/icon/two-inch.png" class="h-[172rpx] w-[332rpx]" />
                </view>
                <view class="mt-[29rpx] h-[172rpx] w-[332rpx]" @click="navigateToCutout">
                  <image src="/static/icon/change-bg.png" class="h-[172rpx] w-[332rpx]" />
                </view>
              </view>
            </view>

            <!-- 选项卡 -->
            <wd-tabs
              v-model="currentTab"
              :list="tabList"
              name-key="name"
              value-key="value"
              swipeable
              @change="handleTabChange"
            />
          </view>
        </wd-sticky>
      </template>

      <!-- 列表内容 -->
      <view class="list-content">
        <view
          v-for="item in listData[tabList[currentTab]?.value || 'recommend']"
          :key="item.id"
          class="list-item"
        >
          <view class="item-content">
            <image :src="item.image" class="item-image" mode="aspectFill" />
            <view class="item-info">
              <text class="item-title">
                {{ item.title }}
              </text>
              <text class="item-description">
                {{ item.description }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.home-container {
  height: 100vh;
  background-color: #f5f5f5;
}

.sticky-header {
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-content {
  padding: 20rpx;
}

.list-item {
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.item-content {
  display: flex;
  padding: 24rpx;
  align-items: center;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.item-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 自定义wd-tabs样式 */
:deep(.wd-tabs) {
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

:deep(.wd-tab) {
  font-size: 30rpx;
  font-weight: 500;
}

:deep(.wd-tab--active) {
  color: #007aff;
  font-weight: 600;
}

/* z-paging样式调整 */
:deep(.z-paging-content) {
  background-color: transparent;
}
</style>
