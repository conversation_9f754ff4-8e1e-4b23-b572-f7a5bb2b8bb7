<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'custom' 表示使用自定义导航栏，可以自定义标题大小
    "navigationStyle": "default",
    "navigationBarTitleText": "首页",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'Home',
})

const userStore = useUserStore()

// 导航到搜索页面
function navigateToSearch() {
  uni.navigateTo({
    url: '/pages/search/index',
  })
}

// 证件照类型导航
function indexGoNextPage(type: number) {
  console.log('选择证件照类型:', type)
  // 这里可以添加导航逻辑，根据type参数跳转到不同页面
  // type 0: 一寸照片
  // type 1: 二寸照片
}

// 导航到换背景页面
function navigateToCutout() {
  uni.navigateTo({
    url: '/pages/cutout/index',
  })
}

// 测试 uni API 自动引入
onLoad(() => {
  console.log('证件照首页加载完成')
})
</script>

<template>
  <view class="bg-white">
    <!-- 搜索框 -->
    <wd-search
      placeholder="搜索证件照名称、尺寸"

      disabled hide-cancel placeholder-left
      custom-style="margin: 10rpx 20rpx;"
      @click="navigateToSearch"
    />

    <!-- 图片布局 -->
    <view class="m-[15rpx_0_30rpx_0] h-[370rpx] w-full flex">
      <view class="ml-[35rpx]" @click="indexGoNextPage(0)">
        <image src="/static/icon/one-inch.png" class="h-[370rpx] w-[328rpx]" />
      </view>
      <view class="ml-[26rpx] w-[50%] flex flex-col">
        <view class="h-[172rpx] w-[332rpx]" @click="indexGoNextPage(1)">
          <image src="/static/icon/two-inch.png" class="h-[172rpx] w-[332rpx]" />
        </view>
        <view class="mt-[29rpx] h-[172rpx] w-[332rpx]" @click="navigateToCutout">
          <image src="/static/icon/change-bg.png" class="h-[172rpx] w-[332rpx]" />
        </view>
      </view>
    </view>
  </view>
</template>
