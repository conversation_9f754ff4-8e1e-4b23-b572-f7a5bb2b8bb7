<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'custom' 表示使用自定义导航栏，可以自定义标题大小
    "navigationStyle": "default",
    "navigationBarTitleText": "首页",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script lang="ts" setup>
defineOptions({
  name: 'Home',
})

// swiper高度
const swiperHeight = ref(0)

// tab列表数据
const tabList = ref(['推荐', '一寸照', '二寸照', '证件照', '免冠照'])

// 当前选中的tab
const currentTab = ref(0)

// z-paging实例引用
const pagingRef = ref()

// swiper列表实例引用
const swiperListRef = ref([])

// 点击搜索框
function handleSearchClick() {
  console.log('点击了搜索框')
  // 这里可以添加搜索相关逻辑
}

// 证件照类型导航
function indexGoNextPage(type: number) {
  console.log('选择证件照类型:', type)
  // 这里可以添加导航逻辑，根据type参数跳转到不同页面
  // type 0: 一寸照片
  // type 1: 二寸照片
}

// 导航到换背景页面
function navigateToCutout() {
  uni.navigateTo({
    url: '/pages/cutout/index',
  })
}

// tabs切换事件
function tabsChange(index: number) {
  setCurrent(index)
}

// 下拉刷新时，通知当前显示的列表进行reload操作
function onRefresh() {
  swiperListRef.value[currentTab.value]?.reload(() => {
    // 当当前显示的列表刷新结束，结束当前页面的刷新状态
    pagingRef.value?.endRefresh()
  })
}

// 当滚动到底部时，通知当前显示的列表加载更多
function scrolltolower() {
  swiperListRef.value[currentTab.value]?.doLoadMore()
}

// swiper滑动结束
function swiperAnimationfinish(e: any) {
  setCurrent(e.detail.current)
}

// 设置swiper的高度
function heightChanged(height: number) {
  if (height === 0) {
    // 默认swiper高度为屏幕可用高度-吸顶区域高度
    // 这里的500是吸顶区域的大概高度，可以根据实际情况调整
    height = uni.getSystemInfoSync().windowHeight - uni.upx2px(500)
  }
  swiperHeight.value = height
}

// 设置当前tab
function setCurrent(current: number) {
  if (current !== currentTab.value) {
    // 显示loading
    uni.showLoading({
      title: '加载中...',
      mask: true,
    })

    // 切换tab时，将上一个tab的数据清空
    swiperListRef.value[currentTab.value]?.clear()
  }
  currentTab.value = current
}

// 测试 uni API 自动引入
onLoad(() => {
  console.log('证件照首页加载完成')
})
</script>

<template>
  <view class="home-container">
    <!-- z-paging容器 -->
    <z-paging
      ref="pagingRef"
      refresher-only
      @on-refresh="onRefresh"
      @scrolltolower="scrolltolower"
    >
      <!-- 搜索框和图片布局 -->
      <view class="banner-view">
        <!-- 搜索框 -->
        <wd-search
          placeholder="搜索证件照名称、尺寸"
          disabled
          hide-cancel
          placeholder-left
          custom-style="margin: 0rpx 20rpx;"
          @click="handleSearchClick"
        />

        <!-- 图片布局 -->
        <view class="m-[15rpx_0_30rpx_0] h-[370rpx] w-full flex">
          <view class="ml-[35rpx]" @click="indexGoNextPage(0)">
            <image src="/static/icon/one-inch.png" class="h-[370rpx] w-[328rpx]" />
          </view>
          <view class="ml-[26rpx] w-[50%] flex flex-col">
            <view class="h-[172rpx] w-[332rpx]" @click="indexGoNextPage(1)">
              <image src="/static/icon/two-inch.png" class="h-[172rpx] w-[332rpx]" />
            </view>
            <view class="mt-[29rpx] h-[172rpx] w-[332rpx]" @click="navigateToCutout">
              <image src="/static/icon/change-bg.png" class="h-[172rpx] w-[332rpx]" />
            </view>
          </view>
        </view>
      </view>

      <!-- 吸顶选项卡和内容区域 -->
      <view>
        <!-- 吸顶选项卡 -->
        <view style="z-index: 100; position: sticky; top: 0;">
          <wd-tabs
            v-model="currentTab"
            @change="tabsChange"
          >
            <wd-tab v-for="(item, index) in tabList" :key="index" :title="item">
              <!-- tab内容由下方的swiper管理，这里为空 -->
            </wd-tab>
          </wd-tabs>
        </view>

        <!-- swiper内容区域 -->
        <swiper
          class="swiper"
          :style="{ height: `${swiperHeight}px` }"
          :current="currentTab"
          @animationfinish="swiperAnimationfinish"
        >
          <swiper-item
            v-for="(_, index) in tabList"
            :key="index"
            class="swiper-item"
          >
            <!-- 每个tab对应的列表组件 -->
            <StickyTabItem
              :ref="(el) => { if (el) swiperListRef[index] = el }"
              :tab-index="index"
              :current-index="currentTab"
              @height-changed="heightChanged"
            />
          </swiper-item>
        </swiper>
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.home-container {
  height: 100vh;
  background-color: #f5f5f5;
}

.banner-view {
  background-color: #fff;
  padding-bottom: 20rpx;
}

.swiper {
  height: 1000px;
}

.swiper-item {
  height: 100%;
}

/* 自定义wd-tabs样式 */
:deep(.wd-tabs) {
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

:deep(.wd-tab) {
  font-size: 30rpx;
  font-weight: 500;
}

:deep(.wd-tab--active) {
  color: #007aff;
  font-weight: 600;
}
</style>
