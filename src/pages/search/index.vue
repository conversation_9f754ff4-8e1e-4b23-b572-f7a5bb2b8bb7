<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "搜索",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script lang="ts" setup>
defineOptions({
  name: 'Search',
})

const searchValue = ref('')
const searchResults = ref([])
const isSearching = ref(false)

// 搜索功能
function handleSearch() {
  if (!searchValue.value.trim()) return
  
  isSearching.value = true
  
  // 模拟搜索请求
  setTimeout(() => {
    const mockResults = []
    for (let i = 1; i <= 10; i++) {
      mockResults.push({
        id: `search_${i}`,
        title: `${searchValue.value}相关结果 ${i}`,
        description: `这是关于"${searchValue.value}"的搜索结果`,
        image: '/static/icon/one-inch.png',
      })
    }
    searchResults.value = mockResults
    isSearching.value = false
  }, 800)
}

// 清空搜索
function handleClear() {
  searchValue.value = ''
  searchResults.value = []
}

// 返回上一页
function goBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="search-page">
    <!-- 搜索框 -->
    <view class="search-header">
      <wd-search
        v-model="searchValue"
        placeholder="搜索证件照名称、尺寸"
        show-action
        @search="handleSearch"
        @clear="handleClear"
        @cancel="goBack"
      />
    </view>

    <!-- 搜索结果 -->
    <view class="search-content">
      <!-- 加载状态 -->
      <view v-if="isSearching" class="loading-container">
        <wd-loading />
        <text class="loading-text">搜索中...</text>
      </view>

      <!-- 搜索结果列表 -->
      <view v-else-if="searchResults.length > 0" class="results-list">
        <view
          v-for="item in searchResults"
          :key="item.id"
          class="result-item"
        >
          <image :src="item.image" class="result-image" mode="aspectFill" />
          <view class="result-info">
            <text class="result-title">{{ item.title }}</text>
            <text class="result-description">{{ item.description }}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="searchValue" class="empty-state">
        <wd-status-tip image="search" tip="暂无搜索结果" />
      </view>

      <!-- 默认状态 -->
      <view v-else class="default-state">
        <wd-status-tip image="search" tip="输入关键词开始搜索" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.search-page {
  height: 100vh;
  background-color: #f5f5f5;
}

.search-header {
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-content {
  flex: 1;
  padding: 20rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.results-list {
  .result-item {
    display: flex;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    align-items: center;
  }

  .result-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
    margin-right: 24rpx;
    flex-shrink: 0;
  }

  .result-info {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .result-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 12rpx;
    line-height: 1.4;
  }

  .result-description {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
}

.empty-state,
.default-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
</style>
